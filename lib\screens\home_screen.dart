import 'package:flutter/material.dart';
import '../services/supabase_service.dart';
import '../models/coffee_shop.dart';
import 'coffee_shop_detail_screen.dart';
import '../widgets/coffee_wave_progress_indicator.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  // Get Supabase service instance
  final SupabaseService _supabaseService = SupabaseService();

  // State variables for managing coffee shop data
  bool _isLoading = true;
  List<CoffeeShop> _coffeeShops = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadCoffeeShops();
  }

  /// Loads coffee shops from Supabase
  ///
  /// This method fetches coffee shop data and updates the UI state accordingly.
  /// It handles both success and error cases with proper state management.
  Future<void> _loadCoffeeShops() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final coffeeShops = await _supabaseService.getCoffeeShops();

      if (mounted) {
        setState(() {
          _coffeeShops = coffeeShops;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load coffee shops: $e';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      appBar: AppBar(
        title: const Text('Discover'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          // Refresh button
          IconButton(
            onPressed: _loadCoffeeShops,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh coffee shops',
          ),
        ],
      ),
      body: Column(
        children: [
          // Supabase connection status indicator
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: _supabaseService.isInitialized
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _supabaseService.isInitialized
                    ? Colors.green
                    : Colors.orange,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _supabaseService.isInitialized
                      ? Icons.cloud_done
                      : Icons.cloud_off,
                  color: _supabaseService.isInitialized
                      ? Colors.green
                      : Colors.orange,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  _supabaseService.isInitialized
                      ? 'Supabase Connected'
                      : 'Supabase Offline',
                  style: TextStyle(
                    color: _supabaseService.isInitialized
                        ? Colors.green
                        : Colors.orange,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          // Main content area
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  /// Builds the main content area based on current state
  ///
  /// Returns different widgets based on loading state, error state, or data state.
  Widget _buildContent() {
    // Show loading indicator
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CoffeeWaveProgressIndicator(size: 80.0),
            SizedBox(height: 16),
            Text(
              'Loading coffee shops...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    // Show error message with retry option
    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'Oops! Something went wrong',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _errorMessage!,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _loadCoffeeShops,
                icon: const Icon(Icons.refresh),
                label: const Text('Try Again'),
              ),
            ],
          ),
        ),
      );
    }

    // Show empty state if no coffee shops found
    if (_coffeeShops.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.coffee_outlined,
                size: 64,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              const Text(
                'No coffee shops found',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'We couldn\'t find any coffee shops in the database.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _loadCoffeeShops,
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh'),
              ),
            ],
          ),
        ),
      );
    }

    // Show coffee shops list
    return _buildCoffeeShopsList();
  }

  /// Builds the coffee shops list view
  ///
  /// Creates a scrollable list of coffee shop cards with proper Material 3 design.
  Widget _buildCoffeeShopsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with count
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Found ${_coffeeShops.length} coffee shop${_coffeeShops.length == 1 ? '' : 's'}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ),
        // Coffee shops list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _coffeeShops.length,
            itemBuilder: (context, index) {
              final coffeeShop = _coffeeShops[index];
              return _buildCoffeeShopCard(coffeeShop);
            },
          ),
        ),
      ],
    );
  }

  /// Builds a single coffee shop card
  ///
  /// Creates a Material 3 styled card for displaying coffee shop information.
  /// Tapping the card navigates to the detailed coffee shop view.
  Widget _buildCoffeeShopCard(CoffeeShop coffeeShop) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          // Navigate to coffee shop detail screen
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CoffeeShopDetailScreen(
                coffeeShop: coffeeShop,
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Coffee shop image or placeholder with Hero animation
              Hero(
                tag: 'coffee_shop_${coffeeShop.id}',
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.grey[200],
                  ),
                  child: coffeeShop.photoUrl != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            coffeeShop.photoUrl!,
                            width: 60,
                            height: 60,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return _buildPlaceholderIcon();
                            },
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return const Center(
                                child: CoffeeWaveProgressIndicator(
                                  size: 40.0,
                                  showPercentage: false,
                                  duration: Duration(seconds: 2),
                                ),
                              );
                            },
                          ),
                        )
                      : _buildPlaceholderIcon(),
                ),
              ),
              const SizedBox(width: 16),
              // Coffee shop information
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name and rating row
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            coffeeShop.name,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (coffeeShop.averageRating != null) ...[
                          const SizedBox(width: 8),
                          Row(
                            children: [
                              const Icon(
                                Icons.star,
                                size: 16,
                                color: Colors.amber,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                coffeeShop.averageRating!.toStringAsFixed(1),
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 4),
                    // Address
                    Text(
                      coffeeShop.address,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    // Description (if available)
                    if (coffeeShop.description != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        coffeeShop.description!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[500],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds a placeholder icon for coffee shops without images
  Widget _buildPlaceholderIcon() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[200],
      ),
      child: const Icon(
        Icons.local_cafe,
        size: 30,
        color: Colors.grey,
      ),
    );
  }
}
